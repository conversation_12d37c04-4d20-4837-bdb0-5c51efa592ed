// ==============================|| OVERRIDES - CHIP ||============================== //

export default function Chip(theme) {
  return {
    MuiChip: {
      styleOverrides: {
        root: {
          borderRadius: "20px",                                    // More rounded for modern look
          fontWeight: 500,                                         // Medium weight for readability
          letterSpacing: "0.3px",                                 // Refined spacing
          border: "1px solid rgba(212, 148, 26, 0.3)",           // Subtle gold border
          backgroundColor: "rgba(212, 148, 26, 0.08)",           // Light gold background
          color: theme.palette.primary.main,                      // Primary color text
          transition: "all 0.2s ease-in-out",                    // Smooth transitions
          "&:hover": {
            backgroundColor: "rgba(212, 148, 26, 0.15)",         // Darker gold on hover
            borderColor: "rgba(212, 148, 26, 0.5)",              // More visible border
            transform: "scale(1.02)",                             // Subtle scale effect
          },
          "&:active": {
            boxShadow: "0 2px 8px rgba(45, 93, 63, 0.2)"        // Active shadow
          }
        },
        sizeLarge: {
          fontSize: "1rem",
          height: 44,                                             // Slightly taller for better proportion
          padding: "0 16px"                                       // More horizontal padding
        },
        light: {
          color: theme.palette.primary.main,
          backgroundColor: theme.palette.primary.lighter,
          borderColor: theme.palette.primary.light,
          "&.MuiChip-lightError": {
            color: theme.palette.error.main,
            backgroundColor: theme.palette.error.lighter,
            borderColor: theme.palette.error.light
          },
          "&.MuiChip-lightSuccess": {
            color: theme.palette.success.main,
            backgroundColor: theme.palette.success.lighter,
            borderColor: theme.palette.success.light
          },
          "&.MuiChip-lightWarning": {
            color: theme.palette.warning.main,
            backgroundColor: theme.palette.warning.lighter,
            borderColor: theme.palette.warning.light
          }
        }
      }
    }
  };
}
