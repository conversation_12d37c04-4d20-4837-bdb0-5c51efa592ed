// material-ui
import { createTheme } from "@mui/material/styles";

// third-party
import { presetPalettes } from "@ant-design/colors";

// project import
import ThemeOption from "./theme";

// ==============================|| DEFAULT THEME - PALETTE  ||============================== //

const Palette = (mode) => {
  const colors = presetPalettes;

  const greyPrimary = [
    "#FFFEFE",
    "#fafafa",
    "#f5f5f5",
    "#f0f0f0",
    "#d9d9d9",
    "#bfbfbf",
    "#8c8c8c",
    "#595959",
    "#262626",
    "#141414",
    "#000000"
  ];
  const greyAscent = ["#fafafa", "#bfbfbf", "#434343", "#1f1f1f"];
  const greyConstant = ["#fafafb", "#e6ebf1"];

  colors.grey = [...greyPrimary, ...greyAscent, ...greyConstant];

  const paletteColor = ThemeOption(colors);

  return createTheme({
    palette: {
      mode,
      common: {
        black: "#1A1A1A",
        white: "#FEFEFE"  // Warm white for restaurant ambiance
      },
      ...paletteColor,
      text: {
        primary: "#2D2D2D",        // Warm dark text
        secondary: paletteColor.grey[600],  // Medium grey for secondary text
        disabled: paletteColor.grey[400]
      },
      action: {
        disabled: paletteColor.grey[300],
        hover: "rgba(45, 93, 63, 0.04)",     // Subtle primary color hover
        selected: "rgba(212, 148, 26, 0.08)" // Subtle secondary color selection
      },
      divider: paletteColor.grey[200],
      background: {
        paper: "#FEFEFE",          // Clean white for cards/papers
        default: "#FAFAF9"         // Warm off-white for main background
      }
    }
  });
};

export default Palette;
