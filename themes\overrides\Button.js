// ==============================|| OVERRIDES - BUTTON ||============================== //

export default function Button(theme) {
  const disabledStyle = {
    "&.Mui-disabled": {
      backgroundColor: theme.palette.grey[200]
    }
  };

  return {
    MuiButton: {
      defaultProps: {
        disableElevation: true,
        disableRipple: true,
      },
      styleOverrides: {
        root: {
          fontWeight: 500,
          fontSize: 16,
          borderRadius: "8px",
          letterSpacing: 0.90
        },
        contained: {
          ...disabledStyle,
          position: "relative",
          // display: "inline-block",
          bgcolor: "#106B30",
          // lineHeight: "26px",
          transition: "all 0.5s ease-out 0s",
          zIndex: 1,
          overflow: "hidden", // Ensure the pseudo-element doesn't overflow the button
          "&::before": {
            content: "''",
            backgroundColor: "#FB8C00", // Use your theme color here
            height: "300px", // Size of the circle
            width: "300px", // Size of the circle
            borderRadius: "50%", // Make it circular
            position: "absolute",
            top: "100%", // Start below the button
            left: "50%", // Center horizontally
            transform: "translateX(-50%)", // Center the circle
            transition: "all 0.5s ease-out", // Smooth transition
            zIndex: -1, // Place it behind the button text
          },
          "&:hover": {
            bgcolor: "transparent", // Make the button background transparent on hover
            "&::before": {
              top: "-150px", // Move the circle upward on hover
            },
          },
        },
        outlined: {
          ...disabledStyle
        },
      }
    }
  };
}
