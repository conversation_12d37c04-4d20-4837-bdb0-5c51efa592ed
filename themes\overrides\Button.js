// ==============================|| OVERRIDES - BUTTON ||============================== //

export default function Button(theme) {
  const disabledStyle = {
    "&.Mui-disabled": {
      backgroundColor: theme.palette.grey[200],
      opacity: 0.6,
      transform: "none",
      boxShadow: "none"
    }
  };

  // Afghan geometric pattern as CSS background
  const afghanPattern = `
    radial-gradient(circle at 25% 25%, rgba(212, 148, 26, 0.1) 2px, transparent 2px),
    radial-gradient(circle at 75% 75%, rgba(212, 148, 26, 0.1) 2px, transparent 2px),
    linear-gradient(45deg, rgba(212, 148, 26, 0.05) 25%, transparent 25%),
    linear-gradient(-45deg, rgba(212, 148, 26, 0.05) 25%, transparent 25%)
  `;

  return {
    MuiButton: {
      defaultProps: {
        disableElevation: false,
        disableRipple: false,
      },
      styleOverrides: {
        root: {
          fontWeight: 600,
          fontSize: "16px",
          borderRadius: "16px",
          letterSpacing: "0.3px",
          textTransform: "none",
          padding: "14px 32px",
          position: "relative",
          overflow: "hidden",
          transition: "all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94)",
          boxShadow: "0 4px 20px rgba(45, 93, 63, 0.15), 0 1px 3px rgba(0, 0, 0, 0.1)",

          // Modern glassmorphism effect
          backdropFilter: "blur(10px)",
          border: "1px solid rgba(255, 255, 255, 0.2)",

          // Subtle Afghan pattern overlay
          "&::before": {
            content: '""',
            position: "absolute",
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            background: afghanPattern,
            backgroundSize: "20px 20px, 20px 20px, 40px 40px, 40px 40px",
            opacity: 0.3,
            transition: "opacity 0.3s ease",
            zIndex: 0,
          },

          // Modern hover animations
          "&:hover": {
            transform: "translateY(-3px) scale(1.02)",
            boxShadow: "0 8px 32px rgba(45, 93, 63, 0.25), 0 4px 12px rgba(0, 0, 0, 0.15)",

            "&::before": {
              opacity: 0.5,
            },

            // Shimmer effect on hover
            "&::after": {
              content: '""',
              position: "absolute",
              top: 0,
              left: "-100%",
              width: "100%",
              height: "100%",
              background: "linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent)",
              animation: "shimmer 0.6s ease-out",
              zIndex: 1,
            }
          },

          "&:active": {
            transform: "translateY(-1px) scale(0.98)",
            transition: "all 0.1s ease",
          },

          // Ensure text is above patterns
          "& .MuiButton-label, & > *": {
            position: "relative",
            zIndex: 2,
          }
        },

        // Modern Primary Button with Afghan geometric patterns
        containedPrimary: {
          background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.dark} 100%)`,
          color: theme.palette.primary.contrastText,
          boxShadow: "0 6px 24px rgba(45, 93, 63, 0.3), 0 2px 8px rgba(0, 0, 0, 0.1)",

          "&:hover": {
            background: `linear-gradient(135deg, ${theme.palette.primary.dark} 0%, ${theme.palette.primary.main} 100%)`,
            boxShadow: "0 10px 40px rgba(45, 93, 63, 0.4), 0 4px 16px rgba(0, 0, 0, 0.15)",
          }
        },

        // Modern Secondary Button with Afghan gold patterns
        containedSecondary: {
          background: `linear-gradient(135deg, ${theme.palette.secondary.main} 0%, ${theme.palette.secondary.dark} 100%)`,
          color: theme.palette.secondary.contrastText,
          boxShadow: "0 6px 24px rgba(212, 148, 26, 0.3), 0 2px 8px rgba(0, 0, 0, 0.1)",

          "&::before": {
            background: `
              radial-gradient(circle at 30% 30%, rgba(255, 255, 255, 0.2) 2px, transparent 2px),
              radial-gradient(circle at 70% 70%, rgba(255, 255, 255, 0.2) 2px, transparent 2px),
              conic-gradient(from 45deg, rgba(255, 255, 255, 0.1), transparent, rgba(255, 255, 255, 0.1))
            `,
            backgroundSize: "16px 16px, 16px 16px, 32px 32px",
          },

          "&:hover": {
            background: `linear-gradient(135deg, ${theme.palette.secondary.dark} 0%, ${theme.palette.secondary.main} 100%)`,
            boxShadow: "0 10px 40px rgba(212, 148, 26, 0.4), 0 4px 16px rgba(0, 0, 0, 0.15)",
          }
        },

        // Modern Outlined Button with Swiss precision
        outlined: {
          border: `2px solid ${theme.palette.primary.main}`,
          color: theme.palette.primary.main,
          background: "rgba(255, 255, 255, 0.8)",
          backdropFilter: "blur(10px)",

          "&::before": {
            background: `
              linear-gradient(45deg, rgba(45, 93, 63, 0.05) 25%, transparent 25%),
              linear-gradient(-45deg, rgba(45, 93, 63, 0.05) 25%, transparent 25%)
            `,
            backgroundSize: "8px 8px, 8px 8px",
          },

          "&:hover": {
            background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.dark} 100%)`,
            color: theme.palette.primary.contrastText,
            border: `2px solid ${theme.palette.primary.dark}`,
            transform: "translateY(-3px) scale(1.02)",
          }
        },

        // Modern Text Button
        text: {
          color: theme.palette.primary.main,
          padding: "12px 24px",

          "&::before": {
            opacity: 0,
          },

          "&:hover": {
            background: `linear-gradient(135deg, rgba(45, 93, 63, 0.1) 0%, rgba(45, 93, 63, 0.05) 100%)`,
            transform: "translateY(-1px)",

            "&::before": {
              opacity: 0.2,
            }
          }
        },

        // Size variants with modern proportions
        sizeSmall: {
          padding: "10px 20px",
          fontSize: "14px",
          borderRadius: "12px",
        },

        sizeMedium: {
          padding: "14px 32px",
          fontSize: "16px",
          borderRadius: "16px",
        },

        sizeLarge: {
          padding: "18px 40px",
          fontSize: "18px",
          borderRadius: "20px",
          fontWeight: 700,
        },

        // Disabled state styling
        "&.Mui-disabled": disabledStyle["&.Mui-disabled"]
      }
    }
  };
}
