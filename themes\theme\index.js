// ==============================|| PRESET THEME - THEME SELECTOR ||============================== //

const Theme = (colors) => {
  const { red, gold, cyan, green, grey } = colors;
  const primaryColor = {
    50: "#E8F5E9",
    100: "#C8E6C9",
    200: "#A5D6A7",
    300: "#81C784",
    400: "#66BB6A",
    500: "#106B30", // Base color
    600: "#0E5A2A",
    700: "#0C4924",
    800: "#0A381E",
    900: "#082718",
    A100: "#C8E6C9", // Optional accent shades
    A200: "#A5D6A7",
    A400: "#66BB6A",
    A700: "#0C4924",
    contrastText: "#FFFFFF",
  };
  
  const secondaryColor = {
    50: "#FFF3E0",
    100: "#FFE0B2",
    200: "#FFCC80",
    300: "#FFB74D",
    400: "#FFA726",
    500: "#FB8C00", // Base secondary color (soft coral/peachy pink)
    600: "#FB8C00",
    700: "#F57C00",
    800: "#EF6C00",
    900: "#E65100",
    A100: "#FFD180", // Optional accent shades
    A200: "#FFAB40",
    A400: "#FF9100",
    A700: "#FF6D00",
    contrastText: "#000000", // Dark text for contrast
  };
  
  const greyColors = {
    0: grey[0],
    50: grey[1],
    100: grey[2],
    200: grey[3],
    300: grey[4],
    400: grey[5],
    500: grey[6],
    600: grey[7],
    700: grey[8],
    800: grey[9],
    900: grey[10],
    A50: grey[15],
    A100: grey[11],
    A200: grey[12],
    A400: grey[13],
    A700: grey[14],
    A800: grey[16]
  };
  const contrastText = "#fff";

  return {
    primary: {
      lighter: primaryColor[50],
      100: primaryColor[100],
      200: primaryColor[200],
      light: primaryColor[300],
      400: primaryColor[400],
      main: primaryColor[500],
      dark: primaryColor[600],
      700: primaryColor[700],
      darker: primaryColor[800],
      900: primaryColor[900],
      contrastText: primaryColor.contrastText
    },
    secondary: {
      lighter: secondaryColor[50],
      100: secondaryColor[100],
      200: secondaryColor[200],
      light: secondaryColor[300],
      400: secondaryColor[400],
      main: secondaryColor[500],
      600: secondaryColor[600],
      dark: secondaryColor[600],
      800: secondaryColor[800],
      darker: secondaryColor[900],
      A100: secondaryColor[50],
      A200: secondaryColor[200],
      A400: secondaryColor[400],
      A700: secondaryColor[700],
      contrastText: secondaryColor.contrastText
    },
    error: {
      lighter: red[0],
      light: red[2],
      main: red[4],
      dark: red[7],
      darker: red[9],
      contrastText
    },
    warning: {
      lighter: gold[0],
      light: gold[3],
      main: gold[5],
      dark: gold[7],
      darker: gold[9],
      contrastText: greyColors[100]
    },
    info: {
      lighter: cyan[0],
      light: cyan[3],
      main: cyan[5],
      dark: cyan[7],
      darker: cyan[9],
      contrastText
    },
    success: {
      lighter: green[0],
      light: green[3],
      main: green[5],
      dark: green[7],
      darker: green[9],
      contrastText
    },
    grey: greyColors
  };
};

export default Theme;
