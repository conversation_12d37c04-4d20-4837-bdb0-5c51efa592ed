const Typography = (fontFamily) => ({
  htmlFontSize: 16,
  fontFamily,
  fontWeightLight: 300,
  fontWeightRegular: 400,
  fontWeightMedium: 500,
  fontWeightBold: 600,
  h1: {
    fontWeight: 700,
    fontSize: "3rem", // Default size for xl screens
    lineHeight: 1.21,
    "@media (max-width: 1266px)": {
      fontSize: "2.7rem", // Adjust for md screens
    },
    "@media (max-width: 1024px)": {
      fontSize: "2.5rem", // Adjust for sm screens
    },
    "@media (max-width: 768px)": {
      fontSize: "2.25rem", // Adjust for xs screens
    },
  },
  h2: {
    fontWeight: 600,
    fontSize: "2.25rem", // Default size for xl screens
    lineHeight: 1.27,
    "@media (max-width: 1536px)": {
      fontSize: "2rem", // Adjust for lg screens
    },
    "@media (max-width: 1266px)": {
      fontSize: "1.875rem", // Adjust for md screens
    },
    "@media (max-width: 1024px)": {
      fontSize: "1.75rem", // Adjust for sm screens
    },
    "@media (max-width: 768px)": {
      fontSize: "1.5rem", // Adjust for xs screens
    },
  },
  h3: {
    fontWeight: 600,
    fontSize: "1.875rem", // Default size for xl screens
    lineHeight: 1.33,
    "@media (max-width: 1536px)": {
      fontSize: "1.75rem", // Adjust for lg screens
    },
    "@media (max-width: 1266px)": {
      fontSize: "1.625rem", // Adjust for md screens
    },
    "@media (max-width: 1024px)": {
      fontSize: "1.5rem", // Adjust for sm screens
    },
    "@media (max-width: 768px)": {
      fontSize: "1.375rem", // Adjust for xs screens
    },
  },
  h4: {
    fontWeight: 600,
    fontSize: "1.5rem", // Default size for xl screens
    lineHeight: 1.4,
    "@media (max-width: 1536px)": {
      fontSize: "1.375rem", // Adjust for lg screens
    },
    "@media (max-width: 1266px)": {
      fontSize: "1.25rem", // Adjust for md screens
    },
    "@media (max-width: 1024px)": {
      fontSize: "1.125rem", // Adjust for sm screens
    },
    "@media (max-width: 768px)": {
      fontSize: "1rem", // Adjust for xs screens
    },
  },
  h5: {
    fontWeight: 600,
    fontSize: "1.25rem", // Default size for xl screens
    lineHeight: 1.5,
    "@media (max-width: 1536px)": {
      fontSize: "1.125rem", // Adjust for lg screens
    },
    "@media (max-width: 1266px)": {
      fontSize: "1rem", // Adjust for md screens
    },
    "@media (max-width: 1024px)": {
      fontSize: "0.9375rem", // Adjust for sm screens
    },
    "@media (max-width: 768px)": {
      fontSize: "0.875rem", // Adjust for xs screens
    },
  },
  h6: {
    fontWeight: 400,
    fontSize: "1rem", // Default size for xl screens
    lineHeight: 1.57,
    "@media (max-width: 1536px)": {
      fontSize: "0.9375rem", // Adjust for lg screens
    },
    "@media (max-width: 1266px)": {
      fontSize: "0.875rem", // Adjust for md screens
    },
    "@media (max-width: 1024px)": {
      fontSize: "0.8125rem", // Adjust for sm screens
    },
    "@media (max-width: 768px)": {
      fontSize: "0.75rem", // Adjust for xs screens
    },
  },
  caption: {
    fontWeight: 400,
    fontSize: "0.875rem",
    lineHeight: 1.66,
  },
  body1: {
    fontSize: "0.9375rem",
    lineHeight: 1.57,
  },
  body2: {
    fontSize: "0.875rem",
    lineHeight: 1.66,
  },
  subtitle1: {
    fontSize: "1rem",
    fontWeight: 500,
    lineHeight: 1.57,
  },
  subtitle2: {
    fontSize: "0.875rem",
    fontWeight: 500,
    lineHeight: 1.66,
  },
  overline: {
    lineHeight: 1.66,
  },
  button: {
    textTransform: "capitalize",
  },
});

export default Typography;