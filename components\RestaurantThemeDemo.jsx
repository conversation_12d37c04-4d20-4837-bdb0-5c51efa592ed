"use client";
import React from 'react';
import {
  Box,
  Typography,
  <PERSON><PERSON>,
  Card,
  CardContent,
  Chip,
  Grid,
  Container,
  Paper
} from '@mui/material';

const RestaurantThemeDemo = () => {
  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      {/* Header Section */}
      <Box sx={{ textAlign: 'center', mb: 6 }}>
        <Typography variant="h1" sx={{ 
          mb: 2,
          background: 'linear-gradient(45deg, #2D5D3F 30%, #D4941A 90%)',
          backgroundClip: 'text',
          WebkitBackgroundClip: 'text',
          WebkitTextFillColor: 'transparent',
        }}>
          Alpen Kabul Restaurant
        </Typography>
        <Typography variant="h4" color="text.secondary" sx={{ mb: 3 }}>
          Where Swiss Precision Meets Afghan Tradition
        </Typography>
        <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center', flexWrap: 'wrap' }}>
          <Chip label="Swiss Alpine" />
          <Chip label="Afghan Cuisine" />
          <Chip label="Modern Dining" />
        </Box>
      </Box>

      {/* Menu Cards Section */}
      <Grid container spacing={4} sx={{ mb: 6 }}>
        <Grid item xs={12} md={4}>
          <Card sx={{ height: '100%' }}>
            <CardContent>
              <Typography variant="h3" sx={{ mb: 2, color: 'primary.main' }}>
                Swiss Classics
              </Typography>
              <Typography variant="body1" sx={{ mb: 3 }}>
                Traditional Swiss dishes prepared with alpine precision and the finest ingredients from the mountains.
              </Typography>
              <Button variant="contained" color="primary" fullWidth>
                Explore Swiss Menu
              </Button>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={4}>
          <Card sx={{ height: '100%' }}>
            <CardContent>
              <Typography variant="h3" sx={{ mb: 2, color: 'secondary.main' }}>
                Afghan Delights
              </Typography>
              <Typography variant="body1" sx={{ mb: 3 }}>
                Authentic Afghan flavors featuring aromatic spices, tender meats, and traditional cooking methods.
              </Typography>
              <Button variant="contained" color="secondary" fullWidth>
                Discover Afghan Cuisine
              </Button>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={4}>
          <Card sx={{ height: '100%' }}>
            <CardContent>
              <Typography variant="h3" sx={{ mb: 2, color: 'primary.dark' }}>
                Fusion Creations
              </Typography>
              <Typography variant="body1" sx={{ mb: 3 }}>
                Innovative dishes that blend Swiss techniques with Afghan spices for a unique culinary experience.
              </Typography>
              <Button variant="outlined" color="primary" fullWidth>
                Try Fusion Menu
              </Button>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Features Section */}
      <Paper sx={{ 
        p: 4, 
        mb: 4,
        background: 'linear-gradient(135deg, rgba(45, 93, 63, 0.05) 0%, rgba(212, 148, 26, 0.05) 100%)',
        border: '1px solid rgba(212, 148, 26, 0.2)'
      }}>
        <Typography variant="h2" sx={{ textAlign: 'center', mb: 4 }}>
          Our Restaurant Experience
        </Typography>
        <Grid container spacing={3}>
          <Grid item xs={12} sm={6} md={3}>
            <Box sx={{ textAlign: 'center' }}>
              <Typography variant="h4" sx={{ mb: 1, color: 'primary.main' }}>
                Alpine Ambiance
              </Typography>
              <Typography variant="body2">
                Swiss mountain-inspired décor with warm Afghan textiles
              </Typography>
            </Box>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Box sx={{ textAlign: 'center' }}>
              <Typography variant="h4" sx={{ mb: 1, color: 'secondary.main' }}>
                Cultural Fusion
              </Typography>
              <Typography variant="body2">
                A harmonious blend of two rich culinary traditions
              </Typography>
            </Box>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Box sx={{ textAlign: 'center' }}>
              <Typography variant="h4" sx={{ mb: 1, color: 'primary.main' }}>
                Fresh Ingredients
              </Typography>
              <Typography variant="body2">
                Locally sourced Swiss produce and authentic Afghan spices
              </Typography>
            </Box>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Box sx={{ textAlign: 'center' }}>
              <Typography variant="h4" sx={{ mb: 1, color: 'secondary.main' }}>
                Warm Hospitality
              </Typography>
              <Typography variant="body2">
                Swiss efficiency combined with Afghan warmth and generosity
              </Typography>
            </Box>
          </Grid>
        </Grid>
      </Paper>

      {/* Call to Action */}
      <Box sx={{ textAlign: 'center' }}>
        <Typography variant="h3" sx={{ mb: 3 }}>
          Reserve Your Table Today
        </Typography>
        <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center', flexWrap: 'wrap' }}>
          <Button variant="contained" color="primary" size="large">
            Make Reservation
          </Button>
          <Button variant="outlined" color="secondary" size="large">
            View Full Menu
          </Button>
        </Box>
      </Box>
    </Container>
  );
};

export default RestaurantThemeDemo;
