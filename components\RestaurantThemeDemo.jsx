"use client";
import React from "react";
import {
  <PERSON>,
  Typo<PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  CardContent,
  <PERSON>,
  Grid,
  Container,
  Paper,
} from "@mui/material";

const RestaurantThemeDemo = () => {
  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      {/* Header Section */}
      <Box
        sx={{
          textAlign: "center",
          mb: 6,
          position: "relative",
          "&::before": {
            content: '""',
            position: "absolute",
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            background: `
            radial-gradient(circle at 30% 30%, rgba(212, 148, 26, 0.1) 2px, transparent 2px),
            radial-gradient(circle at 70% 70%, rgba(45, 93, 63, 0.1) 2px, transparent 2px)
          `,
            backgroundSize: "40px 40px, 60px 60px",
            opacity: 0.3,
            animation: "afghanPattern 12s ease-in-out infinite",
            zIndex: 0,
          },
        }}
      >
        <Typography
          variant="h1"
          sx={{
            mb: 2,
            background:
              "linear-gradient(135deg, #2D5D3F 0%, #D4941A 50%, #2D5D3F 100%)",
            backgroundSize: "200% 200%",
            backgroundClip: "text",
            WebkitBackgroundClip: "text",
            WebkitTextFillColor: "transparent",
            animation: "gradientShift 4s ease infinite",
            position: "relative",
            zIndex: 2,
            textShadow: "0 2px 4px rgba(45, 93, 63, 0.1)",
          }}
        >
          Alpen Kabul Restaurant
        </Typography>
        <Typography
          variant="h4"
          color="text.secondary"
          sx={{
            mb: 3,
            position: "relative",
            zIndex: 2,
            animation: "elegantFadeIn 1s ease-out 0.3s both",
          }}
        >
          Where Swiss Precision Meets Afghan Tradition
        </Typography>
        <Box
          sx={{
            display: "flex",
            gap: 2,
            justifyContent: "center",
            flexWrap: "wrap",
            position: "relative",
            zIndex: 2,
            "& .MuiChip-root": {
              animation: "modernBounce 1s ease-out",
              "&:nth-of-type(1)": { animationDelay: "0.1s" },
              "&:nth-of-type(2)": { animationDelay: "0.2s" },
              "&:nth-of-type(3)": { animationDelay: "0.3s" },
            },
          }}
        >
          <Chip label="Swiss Alpine" className="animate-glow" />
          <Chip label="Afghan Cuisine" />
          <Chip label="Modern Dining" />
        </Box>
      </Box>

      {/* Menu Cards Section */}
      <Grid container spacing={4} sx={{ mb: 6 }}>
        <Grid item xs={12} md={4}>
          <Card sx={{ height: "100%" }}>
            <CardContent>
              <Typography variant="h3" sx={{ mb: 2, color: "primary.main" }}>
                Swiss Classics
              </Typography>
              <Typography variant="body1" sx={{ mb: 3 }}>
                Traditional Swiss dishes prepared with alpine precision and the
                finest ingredients from the mountains.
              </Typography>
              <Button
                variant="contained"
                color="primary"
                fullWidth
                className="hover-lift"
                sx={{
                  animation: "slideInPrecise 0.8s ease-out 0.5s both",
                }}
              >
                Explore Swiss Menu
              </Button>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={4}>
          <Card sx={{ height: "100%" }}>
            <CardContent>
              <Typography variant="h3" sx={{ mb: 2, color: "secondary.main" }}>
                Afghan Delights
              </Typography>
              <Typography variant="body1" sx={{ mb: 3 }}>
                Authentic Afghan flavors featuring aromatic spices, tender
                meats, and traditional cooking methods.
              </Typography>
              <Button
                variant="contained"
                color="secondary"
                fullWidth
                className="hover-scale"
                sx={{
                  animation: "slideInPrecise 0.8s ease-out 0.7s both",
                }}
              >
                Discover Afghan Cuisine
              </Button>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={4}>
          <Card sx={{ height: "100%" }}>
            <CardContent>
              <Typography variant="h3" sx={{ mb: 2, color: "primary.dark" }}>
                Fusion Creations
              </Typography>
              <Typography variant="body1" sx={{ mb: 3 }}>
                Innovative dishes that blend Swiss techniques with Afghan spices
                for a unique culinary experience.
              </Typography>
              <Button
                variant="outlined"
                color="primary"
                fullWidth
                className="animate-pulse hover-lift"
                sx={{
                  animation: "slideInPrecise 0.8s ease-out 0.9s both",
                }}
              >
                Try Fusion Menu
              </Button>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Features Section */}
      <Paper
        sx={{
          p: 4,
          mb: 4,
          position: "relative",
          overflow: "hidden",
          background:
            "linear-gradient(135deg, rgba(45, 93, 63, 0.05) 0%, rgba(212, 148, 26, 0.05) 100%)",
          backgroundSize: "200% 200%",
          border: "1px solid rgba(212, 148, 26, 0.2)",
          borderRadius: "24px",
          backdropFilter: "blur(10px)",
          animation: "gradientShift 6s ease infinite",
          "&::before": {
            content: '""',
            position: "absolute",
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            background: `
              conic-gradient(from 45deg, rgba(212, 148, 26, 0.1), transparent, rgba(45, 93, 63, 0.1), transparent, rgba(212, 148, 26, 0.1)),
              radial-gradient(circle at 25% 25%, rgba(212, 148, 26, 0.08) 3px, transparent 3px),
              radial-gradient(circle at 75% 75%, rgba(45, 93, 63, 0.08) 3px, transparent 3px)
            `,
            backgroundSize: "120px 120px, 30px 30px, 30px 30px",
            opacity: 0.4,
            animation: "afghanPattern 15s ease-in-out infinite",
            zIndex: 0,
          },
          "& > *": {
            position: "relative",
            zIndex: 2,
          },
        }}
      >
        <Typography variant="h2" sx={{ textAlign: "center", mb: 4 }}>
          Our Restaurant Experience
        </Typography>
        <Grid container spacing={3}>
          <Grid item xs={12} sm={6} md={3}>
            <Box sx={{ textAlign: "center" }}>
              <Typography variant="h4" sx={{ mb: 1, color: "primary.main" }}>
                Alpine Ambiance
              </Typography>
              <Typography variant="body2">
                Swiss mountain-inspired décor with warm Afghan textiles
              </Typography>
            </Box>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Box sx={{ textAlign: "center" }}>
              <Typography variant="h4" sx={{ mb: 1, color: "secondary.main" }}>
                Cultural Fusion
              </Typography>
              <Typography variant="body2">
                A harmonious blend of two rich culinary traditions
              </Typography>
            </Box>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Box sx={{ textAlign: "center" }}>
              <Typography variant="h4" sx={{ mb: 1, color: "primary.main" }}>
                Fresh Ingredients
              </Typography>
              <Typography variant="body2">
                Locally sourced Swiss produce and authentic Afghan spices
              </Typography>
            </Box>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Box sx={{ textAlign: "center" }}>
              <Typography variant="h4" sx={{ mb: 1, color: "secondary.main" }}>
                Warm Hospitality
              </Typography>
              <Typography variant="body2">
                Swiss efficiency combined with Afghan warmth and generosity
              </Typography>
            </Box>
          </Grid>
        </Grid>
      </Paper>

      {/* Call to Action */}
      <Box sx={{ textAlign: "center" }}>
        <Typography variant="h3" sx={{ mb: 3 }}>
          Reserve Your Table Today
        </Typography>
        <Box
          sx={{
            display: "flex",
            gap: 2,
            justifyContent: "center",
            flexWrap: "wrap",
          }}
        >
          <Button
            variant="contained"
            color="primary"
            size="large"
            className="animate-pulse hover-lift"
            sx={{
              animation: "modernBounce 1s ease-out 1.2s both",
              boxShadow: "0 8px 32px rgba(45, 93, 63, 0.3)",
            }}
          >
            Make Reservation
          </Button>
          <Button
            variant="outlined"
            color="secondary"
            size="large"
            className="hover-scale"
            sx={{
              animation: "modernBounce 1s ease-out 1.4s both",
            }}
          >
            View Full Menu
          </Button>
        </Box>
      </Box>
    </Container>
  );
};

export default RestaurantThemeDemo;
