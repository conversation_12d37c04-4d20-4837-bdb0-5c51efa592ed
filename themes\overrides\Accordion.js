export default function Accordion(theme) {
  return {
    MuiAccordion: {
      styleOverrides: {
        root: {
          boxShadow: "rgba(0, 0, 0, 0.05) 0px 0px 0px 1px;",
          borderRadius: "4px !important",
          marginBottom: "10px",
          "&:before": {
            display: "none",
          },
          "& .Mui-expanded": {
            margin: "auto",
          },
        },
      },
    },
    MuiAccordionSummary: {
      styleOverrides: {
        root: {
          backgroundColor: theme.palette.background.paper,
          borderBottom: `1px solid ${theme.palette.divider}`,
          "& .MuiAccordionSummary-content": {
            margin: "12px 0",
          },
        },
      },
    },
    MuiAccordionDetails: {
      styleOverrides: {
        root: {
          padding: theme.spacing(2),
        },
      },
    },
  };
}