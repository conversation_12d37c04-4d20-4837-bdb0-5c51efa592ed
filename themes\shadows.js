// material-ui
import { alpha } from "@mui/material";

// ==============================|| SWISS-AFGHAN RESTAURANT THEME - CUSTOM SHADOWS  ||============================== //

const CustomShadows = (theme) => ({
  // Swiss-inspired clean shadows with subtle warmth
  button: "0 3px 12px rgba(45, 93, 63, 0.15)",                    // Subtle green shadow for buttons
  text: "0 1px 3px rgba(0, 0, 0, 0.12)",                         // Clean text shadow

  // Afghan-inspired warm shadows with gold undertones
  card: "0 4px 20px rgba(45, 93, 63, 0.08)",                     // Card shadows with green tint
  cardHover: "0 8px 32px rgba(45, 93, 63, 0.12)",               // Enhanced hover shadows

  // Restaurant-specific shadows
  menu: "0 6px 24px rgba(212, 148, 26, 0.15)",                  // Warm gold shadow for menu items
  header: "0 2px 16px rgba(45, 93, 63, 0.1)",                   // Header shadow

  // Elevation levels with Swiss-Afghan color influence
  z1: `0px 2px 8px ${alpha(theme.palette.primary.main, 0.15)}`,  // Light elevation
  z4: `0px 4px 16px ${alpha(theme.palette.primary.main, 0.2)}`,  // Medium elevation
  z8: `0px 8px 32px ${alpha(theme.palette.primary.main, 0.25)}`, // High elevation
  z12: `0px 12px 48px ${alpha(theme.palette.secondary.main, 0.2)}`, // Maximum elevation with gold

  // Special restaurant ambiance shadows
  warm: `0 4px 20px ${alpha(theme.palette.secondary.main, 0.2)}`, // Warm golden glow
  alpine: `0 2px 12px ${alpha(theme.palette.primary.main, 0.18)}`, // Cool alpine shadow
});

export default CustomShadows;
