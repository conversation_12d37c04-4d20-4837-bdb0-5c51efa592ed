// material-ui
import { alpha } from "@mui/material";

// ==============================|| SWISS-AFGHAN RESTAURANT THEME - CUSTOM SHADOWS  ||============================== //

const CustomShadows = (theme) => ({
  // Modern button shadows with depth and sophistication
  button: `
    0 4px 14px 0 rgba(45, 93, 63, 0.2),
    0 2px 6px 0 rgba(0, 0, 0, 0.05),
    inset 0 1px 0 rgba(255, 255, 255, 0.1)
  `,

  buttonHover: `
    0 8px 28px 0 rgba(45, 93, 63, 0.3),
    0 4px 12px 0 rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2)
  `,

  buttonActive: `
    0 2px 8px 0 rgba(45, 93, 63, 0.25),
    0 1px 3px 0 rgba(0, 0, 0, 0.1),
    inset 0 1px 2px rgba(0, 0, 0, 0.1)
  `,

  // Modern card shadows with glassmorphism effect
  card: `
    0 8px 32px rgba(45, 93, 63, 0.12),
    0 2px 8px rgba(0, 0, 0, 0.04),
    inset 0 1px 0 rgba(255, 255, 255, 0.1)
  `,

  cardHover: `
    0 16px 48px rgba(45, 93, 63, 0.18),
    0 4px 16px rgba(0, 0, 0, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.15)
  `,

  // Afghan-inspired ornate shadows with warm gold undertones
  ornate: `
    0 6px 24px rgba(212, 148, 26, 0.2),
    0 2px 8px rgba(212, 148, 26, 0.1),
    0 12px 48px rgba(45, 93, 63, 0.08)
  `,

  ornateHover: `
    0 12px 40px rgba(212, 148, 26, 0.3),
    0 4px 16px rgba(212, 148, 26, 0.15),
    0 20px 64px rgba(45, 93, 63, 0.12)
  `,

  // Modern text shadows for elegant typography
  text: "0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24)",
  textElegant: `
    0 2px 4px rgba(45, 93, 63, 0.1),
    0 1px 2px rgba(212, 148, 26, 0.1)
  `,

  // Restaurant ambiance shadows
  menu: `
    0 8px 32px rgba(212, 148, 26, 0.15),
    0 2px 8px rgba(0, 0, 0, 0.05),
    inset 0 1px 0 rgba(255, 255, 255, 0.1)
  `,

  header: `
    0 4px 20px rgba(45, 93, 63, 0.12),
    0 1px 4px rgba(0, 0, 0, 0.05)
  `,

  // Modern elevation system with cultural touches
  z1: `
    0 2px 8px ${alpha(theme.palette.primary.main, 0.15)},
    0 1px 3px rgba(0, 0, 0, 0.05)
  `,

  z4: `
    0 4px 16px ${alpha(theme.palette.primary.main, 0.2)},
    0 2px 6px rgba(0, 0, 0, 0.08)
  `,

  z8: `
    0 8px 32px ${alpha(theme.palette.primary.main, 0.25)},
    0 4px 12px rgba(0, 0, 0, 0.1)
  `,

  z12: `
    0 12px 48px ${alpha(theme.palette.secondary.main, 0.2)},
    0 6px 20px ${alpha(theme.palette.primary.main, 0.15)},
    0 2px 8px rgba(0, 0, 0, 0.1)
  `,

  z16: `
    0 16px 64px ${alpha(theme.palette.secondary.main, 0.25)},
    0 8px 28px ${alpha(theme.palette.primary.main, 0.2)},
    0 4px 12px rgba(0, 0, 0, 0.12)
  `,

  // Special atmospheric shadows
  warm: `
    0 6px 24px ${alpha(theme.palette.secondary.main, 0.2)},
    0 2px 8px ${alpha(theme.palette.secondary.main, 0.1)},
    0 12px 48px rgba(212, 148, 26, 0.08)
  `,

  alpine: `
    0 4px 16px ${alpha(theme.palette.primary.main, 0.18)},
    0 1px 4px ${alpha(theme.palette.primary.main, 0.1)},
    0 8px 32px rgba(45, 93, 63, 0.06)
  `,

  // Modern glassmorphism shadows
  glass: `
    0 8px 32px rgba(255, 255, 255, 0.1),
    0 2px 8px rgba(0, 0, 0, 0.05),
    inset 0 1px 0 rgba(255, 255, 255, 0.2)
  `,

  glassHover: `
    0 12px 48px rgba(255, 255, 255, 0.15),
    0 4px 16px rgba(0, 0, 0, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.3)
  `,
});

export default CustomShadows;
