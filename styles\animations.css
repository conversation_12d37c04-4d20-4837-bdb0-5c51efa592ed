/* Modern Swiss-Afghan Restaurant Theme Animations */

/* Shimmer effect for buttons */
@keyframes shimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

/* Floating animation for cards */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-6px);
  }
}

/* Pulse animation for interactive elements */
@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(45, 93, 63, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(45, 93, 63, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(45, 93, 63, 0);
  }
}

/* Afghan geometric pattern animation */
@keyframes afghanPattern {
  0% {
    background-position: 0% 0%;
  }
  50% {
    background-position: 100% 100%;
  }
  100% {
    background-position: 0% 0%;
  }
}

/* Swiss precision slide-in animation */
@keyframes slideInPrecise {
  0% {
    opacity: 0;
    transform: translateX(-30px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Elegant fade-in with scale */
@keyframes elegantFadeIn {
  0% {
    opacity: 0;
    transform: scale(0.95) translateY(10px);
  }
  100% {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* Modern bounce effect */
@keyframes modernBounce {
  0%, 20%, 53%, 80%, 100% {
    animation-timing-function: cubic-bezier(0.215, 0.610, 0.355, 1.000);
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    animation-timing-function: cubic-bezier(0.755, 0.050, 0.855, 0.060);
    transform: translate3d(0, -8px, 0);
  }
  70% {
    animation-timing-function: cubic-bezier(0.755, 0.050, 0.855, 0.060);
    transform: translate3d(0, -4px, 0);
  }
  90% {
    transform: translate3d(0, -2px, 0);
  }
}

/* Gradient animation for backgrounds */
@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* Ripple effect for interactions */
@keyframes ripple {
  0% {
    transform: scale(0);
    opacity: 1;
  }
  100% {
    transform: scale(4);
    opacity: 0;
  }
}

/* Smooth reveal animation */
@keyframes reveal {
  0% {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Glow effect for special elements */
@keyframes glow {
  0%, 100% {
    box-shadow: 0 0 5px rgba(212, 148, 26, 0.3);
  }
  50% {
    box-shadow: 0 0 20px rgba(212, 148, 26, 0.6), 0 0 30px rgba(212, 148, 26, 0.4);
  }
}

/* Utility classes for animations */
.animate-shimmer {
  animation: shimmer 0.6s ease-out;
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-pulse {
  animation: pulse 2s infinite;
}

.animate-pattern {
  animation: afghanPattern 8s ease-in-out infinite;
}

.animate-slide-in {
  animation: slideInPrecise 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.animate-fade-in {
  animation: elegantFadeIn 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.animate-bounce {
  animation: modernBounce 1s;
}

.animate-gradient {
  animation: gradientShift 3s ease infinite;
  background-size: 200% 200%;
}

.animate-ripple {
  animation: ripple 0.6s linear;
}

.animate-reveal {
  animation: reveal 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.animate-glow {
  animation: glow 2s ease-in-out infinite alternate;
}

/* Hover effects */
.hover-lift {
  transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.hover-lift:hover {
  transform: translateY(-4px);
}

.hover-scale {
  transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.hover-scale:hover {
  transform: scale(1.05);
}

/* Modern glassmorphism utilities */
.glass-effect {
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.glass-effect-dark {
  backdrop-filter: blur(10px);
  background: rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Afghan pattern overlays */
.afghan-pattern-overlay {
  position: relative;
}

.afghan-pattern-overlay::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    radial-gradient(circle at 25% 25%, rgba(212, 148, 26, 0.1) 2px, transparent 2px),
    radial-gradient(circle at 75% 75%, rgba(212, 148, 26, 0.1) 2px, transparent 2px),
    linear-gradient(45deg, rgba(212, 148, 26, 0.05) 25%, transparent 25%),
    linear-gradient(-45deg, rgba(212, 148, 26, 0.05) 25%, transparent 25%);
  background-size: 20px 20px, 20px 20px, 40px 40px, 40px 40px;
  opacity: 0.3;
  pointer-events: none;
  z-index: 1;
}

/* Swiss precision grid overlay */
.swiss-grid-overlay {
  position: relative;
}

.swiss-grid-overlay::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    linear-gradient(45deg, rgba(45, 93, 63, 0.03) 25%, transparent 25%),
    linear-gradient(-45deg, rgba(45, 93, 63, 0.03) 25%, transparent 25%);
  background-size: 16px 16px, 16px 16px;
  opacity: 0.5;
  pointer-events: none;
  z-index: 1;
}
