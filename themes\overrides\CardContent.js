// ==============================|| OVERRIDES - CARD CONTENT ||============================== //

export default function CardContent() {
  return {
    MuiCard: {
      styleOverrides: {
        root: {
          borderRadius: "16px",                                    // Rounded corners for modern feel
          boxShadow: "0 4px 20px rgba(45, 93, 63, 0.08)",        // Subtle green-tinted shadow
          border: "1px solid rgba(212, 148, 26, 0.12)",          // Subtle gold border
          transition: "all 0.3s cubic-bezier(0.4, 0, 0.2, 1)",  // Smooth transitions
          "&:hover": {
            boxShadow: "0 8px 32px rgba(45, 93, 63, 0.12)",      // Enhanced hover shadow
            transform: "translateY(-2px)",                        // Subtle lift on hover
            borderColor: "rgba(212, 148, 26, 0.2)",             // More visible gold border on hover
          }
        }
      }
    },
    MuiCardContent: {
      styleOverrides: {
        root: {
          padding: "24px",                                         // More generous padding for restaurant feel
          "&:last-child": {
            paddingBottom: "24px"                                 // Consistent bottom padding
          }
        }
      }
    }
  };
}
