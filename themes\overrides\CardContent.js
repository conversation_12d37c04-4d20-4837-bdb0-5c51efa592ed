// ==============================|| OVERRIDES - CARD CONTENT ||============================== //

export default function CardContent() {
  return {
    MuiCard: {
      styleOverrides: {
        root: {
          borderRadius: "20px",
          position: "relative",
          overflow: "hidden",
          background: "rgba(255, 255, 255, 0.95)",
          backdropFilter: "blur(10px)",
          border: "1px solid rgba(212, 148, 26, 0.15)",
          boxShadow: `
            0 8px 32px rgba(45, 93, 63, 0.12),
            0 2px 8px rgba(0, 0, 0, 0.04),
            inset 0 1px 0 rgba(255, 255, 255, 0.1)
          `,
          transition: "all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94)",

          // Afghan geometric pattern overlay
          "&::before": {
            content: '""',
            position: "absolute",
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            background: `
              radial-gradient(circle at 20% 20%, rgba(212, 148, 26, 0.08) 2px, transparent 2px),
              radial-gradient(circle at 80% 80%, rgba(212, 148, 26, 0.08) 2px, transparent 2px),
              linear-gradient(45deg, rgba(45, 93, 63, 0.03) 25%, transparent 25%),
              linear-gradient(-45deg, rgba(45, 93, 63, 0.03) 25%, transparent 25%)
            `,
            backgroundSize: "24px 24px, 24px 24px, 48px 48px, 48px 48px",
            opacity: 0.4,
            transition: "opacity 0.3s ease",
            zIndex: 0,
          },

          "&:hover": {
            transform: "translateY(-6px) scale(1.02)",
            boxShadow: `
              0 16px 48px rgba(45, 93, 63, 0.18),
              0 4px 16px rgba(0, 0, 0, 0.08),
              inset 0 1px 0 rgba(255, 255, 255, 0.15)
            `,
            borderColor: "rgba(212, 148, 26, 0.3)",

            "&::before": {
              opacity: 0.6,
            },

            // Shimmer effect on hover
            "&::after": {
              content: '""',
              position: "absolute",
              top: 0,
              left: "-100%",
              width: "100%",
              height: "100%",
              background: "linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent)",
              animation: "shimmer 0.8s ease-out",
              zIndex: 1,
            }
          },

          // Ensure content is above patterns
          "& > *": {
            position: "relative",
            zIndex: 2,
          }
        }
      }
    },
    MuiCardContent: {
      styleOverrides: {
        root: {
          padding: "24px",                                         // More generous padding for restaurant feel
          "&:last-child": {
            paddingBottom: "24px"                                 // Consistent bottom padding
          }
        }
      }
    }
  };
}
