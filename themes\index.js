"use client";
import PropTypes from "prop-types";
import { useMemo } from "react";

// material-ui
import { CssBaseline, StyledEngineProvider } from "@mui/material";
import { createTheme, ThemeProvider } from "@mui/material/styles";
import "@fontsource/playfair-display";
import "@fontsource/inter";

// project import
import Palette from "./palette";
import Typography from "./typography";
import CustomShadows from "./shadows";
import componentsOverride from "./overrides";
import { AppRouterCacheProvider } from "@mui/material-nextjs/v15-appRouter";

// ==============================|| DEFAULT THEME - MAIN  ||============================== //

export default function ThemeCustomization({ children }) {
  const theme = Palette("light", "default");

     
  // Swiss-Afghan Restaurant Typography: Elegant headings with clean body text
  const themeTypography = Typography(
    "'Inter', 'Helvetica Neue', Arial, sans-serif",  // Clean, Swiss-inspired body font
    "'Playfair Display', 'Times New Roman', serif"   // Elegant, Afghan-inspired heading font
  );
  const themeCustomShadows = useMemo(() => CustomShadows(theme), [theme]);

  const themeOptions = useMemo(
    () => ({
      breakpoints: {
        values: {
          xs: 0,
          sm: 768,
          md: 1024,
          lg: 1266,
          xl: 1536
        }
      },
      direction: "ltr",
      // mixins: {
      //   toolbar: {
      //     minHeight: 60,
      //     paddingTop: 8,
      //     paddingBottom: 8
      //   }
      // },
      palette: theme.palette,
      customShadows: themeCustomShadows,
      typography: themeTypography
    }),
    [theme, themeTypography, themeCustomShadows]
  );

  const themes = createTheme(themeOptions);
  themes.components = componentsOverride(themes);
  return (
    <StyledEngineProvider injectFirst>
      <AppRouterCacheProvider options={{ enableCssLayer: true }}>
        <ThemeProvider theme={themes}>
          <CssBaseline />
          {children}
        </ThemeProvider>
      </AppRouterCacheProvider>
    </StyledEngineProvider>
  );
}

ThemeCustomization.propTypes = {
  children: PropTypes.node
};
